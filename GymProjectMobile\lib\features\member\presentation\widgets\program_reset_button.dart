/// Program Reset Button Widget - GymKod Pro Mobile
///
/// Bu widget tüm antrenmanlar tamamlandığında program sıfırlama butonu sağlar.
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/exercise_progress_provider.dart';
import '../providers/workout_program_detail_provider.dart';
import '../../../../core/core.dart';

/// Program sıfırlama butonu widget'ı
class ProgramResetButton extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Program adı
  final String programName;
  
  /// Tüm antrenman günlerinin ID'leri
  final List<int> allWorkoutDayIds;
  
  /// Tüm egzersizlerin ID'leri (gün bazında)
  final Map<int, List<int>> allExerciseIds;

  const ProgramResetButton({
    super.key,
    required this.programId,
    required this.programName,
    required this.allWorkoutDayIds,
    required this.allExerciseIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Tüm antrenman günlerinin tamamlanıp tamamlanmadığını kontrol et
    bool allWorkoutsCompleted = true;
    for (final dayId in allWorkoutDayIds) {
      final sessionKey = '${programId}_$dayId';
      final isCompleted = ref.watch(workoutSessionCompletionProvider(sessionKey));
      if (!isCompleted) {
        allWorkoutsCompleted = false;
        break;
      }
    }

    // Eğer tüm antrenmanlar tamamlanmadıysa butonu gösterme
    if (!allWorkoutsCompleted) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.secondary,
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ElevatedButton.icon(
          onPressed: () => _resetProgramDirectly(context, ref),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.white,
            shadowColor: Colors.transparent,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          icon: const Icon(Icons.refresh, size: 22),
          label: const Text(
            'Programı Baştan Başlat',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// Direkt program sıfırlama (onay olmadan)
  Future<void> _resetProgramDirectly(BuildContext context, WidgetRef ref) async {
    try {
      // Haptic feedback
      HapticFeedback.mediumImpact();

      LoggingService.info('Direct program reset started', tag: 'PROGRAM_RESET');

      // Tüm günlerin egzersizlerini sıfırla
      for (int i = 0; i < allWorkoutDayIds.length; i++) {
        final dayId = allWorkoutDayIds[i];
        final exerciseIds = allExerciseIds[dayId] ?? [];

        LoggingService.info('Resetting day $dayId with ${exerciseIds.length} exercises (${i + 1}/${allWorkoutDayIds.length})', tag: 'PROGRAM_RESET');

        if (exerciseIds.isNotEmpty) {
          await ref.read(exerciseProgressProvider.notifier).resetDayExercises(
            programId,
            dayId,
            exerciseIds,
          );
        }
      }

      // Program detayını yeniden yükle (UI'ın güncellenmesi için)
      if (context.mounted) {
        try {
          await ref.read(workoutProgramDetailProvider.notifier).refreshProgramDetail(programId);
          LoggingService.info('Program detail refreshed after reset', tag: 'PROGRAM_RESET');
        } catch (refreshError) {
          LoggingService.warning('Program detail refresh error: $refreshError', tag: 'PROGRAM_RESET');
        }
      }

      // Başarı mesajı göster (kısa snackbar)
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Program başarıyla sıfırlandı! 🎉',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 3),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }

      LoggingService.info('Direct program reset completed successfully', tag: 'PROGRAM_RESET');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ProgramResetButton._resetProgramDirectly',
      );

      // Hata mesajı göster
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(
                  Icons.error,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Program sıfırlama sırasında hata oluştu',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        );
      }
    }
  }
}
