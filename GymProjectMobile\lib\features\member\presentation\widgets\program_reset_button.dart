/// Program Reset Button Widget - GymKod Pro Mobile
///
/// Bu widget tüm antrenmanlar tamamlandığında program sıfırlama butonu sağlar.
library;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/exercise_progress_provider.dart';
import '../providers/workout_program_detail_provider.dart';
import '../../../../core/core.dart';

/// Program sıfırlama butonu widget'ı
class ProgramResetButton extends ConsumerWidget {
  /// Program ID'si
  final int programId;
  
  /// Program adı
  final String programName;
  
  /// Tüm antrenman günlerinin ID'leri
  final List<int> allWorkoutDayIds;
  
  /// Tüm egzersizlerin ID'leri (gün bazında)
  final Map<int, List<int>> allExerciseIds;

  const ProgramResetButton({
    super.key,
    required this.programId,
    required this.programName,
    required this.allWorkoutDayIds,
    required this.allExerciseIds,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Tüm antrenman günlerinin tamamlanıp tamamlanmadığını kontrol et
    bool allWorkoutsCompleted = true;
    for (final dayId in allWorkoutDayIds) {
      final sessionKey = '${programId}_$dayId';
      final isCompleted = ref.watch(workoutSessionCompletionProvider(sessionKey));
      if (!isCompleted) {
        allWorkoutsCompleted = false;
        break;
      }
    }

    // Eğer tüm antrenmanlar tamamlanmadıysa butonu gösterme
    if (!allWorkoutsCompleted) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              theme.colorScheme.primary,
              theme.colorScheme.secondary,
            ],
            begin: Alignment.centerLeft,
            end: Alignment.centerRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: theme.colorScheme.primary.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ElevatedButton.icon(
          onPressed: () => _showResetConfirmationDialog(context, ref),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.transparent,
            foregroundColor: Colors.white,
            shadowColor: Colors.transparent,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
          icon: const Icon(Icons.refresh, size: 22),
          label: const Text(
            'Programı Baştan Başlat',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ),
    );
  }

  /// Sıfırlama onay dialog'u
  void _showResetConfirmationDialog(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        backgroundColor: theme.colorScheme.surface,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(
            maxWidth: 400,
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.secondary,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.refresh,
                  color: Colors.white,
                  size: 40,
                ),
              ),

              const SizedBox(height: 24),

              // Title
              Text(
                'Program Yeniden Başlat',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),

              const SizedBox(height: 16),

              // Content
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.primary.withValues(alpha: 0.2),
                  ),
                ),
                child: Column(
                  children: [
                    Text(
                      'Tebrikler! 🎉',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                        fontSize: 18,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '"$programName" programının tüm antrenmanlarını tamamladınız!',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 16,
                        color: theme.colorScheme.onSurface,
                        height: 1.4,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 20),

              Text(
                'Programı yeniden başlatmak istiyor musunuz?',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  height: 1.3,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
              ),

              const SizedBox(height: 32),

              // Buttons (Alt alta)
              Column(
                children: [
                  // Yeniden Başlat Butonu
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.pop(context);
                        _resetProgram(context, ref);
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: theme.colorScheme.primary,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                        elevation: 2,
                      ),
                      child: const Text(
                        'Yeniden Başlat',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),

                  const SizedBox(height: 12),

                  // İptal Butonu
                  SizedBox(
                    width: double.infinity,
                    height: 50,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: theme.colorScheme.outline,
                          width: 1.5,
                        ),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          foregroundColor: theme.colorScheme.onSurfaceVariant,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                        child: const Text(
                          'İptal',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Program sıfırlama işlemi
  Future<void> _resetProgram(BuildContext context, WidgetRef ref) async {
    bool isDialogShown = false;

    try {
      // Haptic feedback
      HapticFeedback.mediumImpact();

      // Loading dialog göster
      if (context.mounted) {
        isDialogShown = true;
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => PopScope(
            canPop: false, // Back button'u devre dışı bırak
            child: const AlertDialog(
              content: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      'Program sıfırlanıyor...',
                      style: TextStyle(fontSize: 16),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      }

      LoggingService.info('Starting program reset for ${allWorkoutDayIds.length} days', tag: 'PROGRAM_RESET');

      // Tüm günlerin egzersizlerini sıfırla
      for (int i = 0; i < allWorkoutDayIds.length; i++) {
        final dayId = allWorkoutDayIds[i];
        final exerciseIds = allExerciseIds[dayId] ?? [];

        LoggingService.info('Resetting day $dayId with ${exerciseIds.length} exercises (${i + 1}/${allWorkoutDayIds.length})', tag: 'PROGRAM_RESET');

        if (exerciseIds.isNotEmpty) {
          await ref.read(exerciseProgressProvider.notifier).resetDayExercises(
            programId,
            dayId,
            exerciseIds,
          );
        }

        // Kısa bir bekleme ekle (UI'ın donmaması için)
        await Future.delayed(const Duration(milliseconds: 50));
      }

      // Loading dialog'u güvenli şekilde kapat
      if (isDialogShown && context.mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
          isDialogShown = false;
        } catch (e) {
          LoggingService.warning('Dialog kapatma hatası: $e', tag: 'PROGRAM_RESET');
        }
      }

      // Kısa bir bekleme (UI'ın güncellenmesi için)
      await Future.delayed(const Duration(milliseconds: 100));

      // Program detayını yeniden yükle (UI'ın güncellenmesi için)
      if (context.mounted) {
        try {
          await ref.read(workoutProgramDetailProvider.notifier).refreshProgramDetail(programId);
          LoggingService.info('Program detail refreshed after reset', tag: 'PROGRAM_RESET');
        } catch (refreshError) {
          LoggingService.warning('Program detail refresh error: $refreshError', tag: 'PROGRAM_RESET');
        }
      }

      // Başarı mesajı göster
      if (context.mounted) {
        _showSuccessDialog(context);
      }

      LoggingService.info('Program reset completed successfully', tag: 'PROGRAM_RESET');
    } catch (e, stackTrace) {
      LoggingService.logException(
        e,
        stackTrace,
        context: 'ProgramResetButton._resetProgram',
      );

      // Loading dialog'u güvenli şekilde kapat
      if (isDialogShown && context.mounted) {
        try {
          Navigator.of(context, rootNavigator: true).pop();
        } catch (dialogError) {
          LoggingService.warning('Dialog kapatma hatası: $dialogError', tag: 'PROGRAM_RESET');
        }
      }

      // Hata mesajı göster
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Program sıfırlama işlemi sırasında hata oluştu'),
            backgroundColor: Theme.of(context).colorScheme.error,
            duration: const Duration(seconds: 4),
          ),
        );
      }
    }
  }

  /// Başarı dialog'u
  void _showSuccessDialog(BuildContext context) {
    final theme = Theme.of(context);

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        backgroundColor: theme.colorScheme.surface,
        child: Container(
          width: MediaQuery.of(context).size.width * 0.9,
          constraints: const BoxConstraints(
            maxWidth: 400,
          ),
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Header Icon
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      theme.colorScheme.primary,
                      theme.colorScheme.secondary,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(40),
                ),
                child: const Icon(
                  Icons.check_circle,
                  color: Colors.white,
                  size: 40,
                ),
              ),

              const SizedBox(height: 24),

              // Title
              Text(
                'Başarılı! 🎉',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 22,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),

              const SizedBox(height: 16),

              // Content
              Text(
                '"$programName" programı başarıyla sıfırlandı!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                  height: 1.4,
                ),
              ),

              const SizedBox(height: 20),

              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: theme.colorScheme.primary.withValues(alpha: 0.2),
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.fitness_center,
                      color: theme.colorScheme.primary,
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Artık programınıza yeniden başlayabilirsiniz!',
                        style: TextStyle(
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 32),

              // Button
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: theme.colorScheme.primary,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    elevation: 2,
                  ),
                  child: const Text(
                    'Harika! 💪',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
